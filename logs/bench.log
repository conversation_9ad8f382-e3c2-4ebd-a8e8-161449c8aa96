2025-06-16 13:26:31,840 DEBUG cd frappe-bench && python3 -m venv env
2025-06-16 13:26:35,384 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install  --upgrade pip
2025-06-16 13:26:38,189 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install  wheel
2025-06-16 13:26:39,833 LOG Getting frappe
2025-06-16 13:26:39,833 DEBUG cd frappe-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-06-16 13:27:23,812 LOG Installing frappe
2025-06-16 13:27:23,814 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install  --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-06-16 13:28:41,778 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files --verbose
2025-06-16 13:29:12,979 DEBUG cd frappe-bench && bench build
2025-06-16 13:29:13,139 INFO /usr/local/bin/bench build
2025-06-16 13:29:35,448 LOG setting up backups
2025-06-16 13:29:35,474 LOG backups were set up
2025-06-16 13:29:35,474 INFO Bench frappe-bench initialized
2025-06-16 13:30:01,675 INFO /usr/local/bin/bench new-site buildo --db-root-username root --db-root-password 123 --admin-password 123
2025-06-16 13:30:48,938 INFO /usr/local/bin/bench use buildo
2025-06-16 13:30:49,503 INFO /usr/local/bin/bench build
2025-06-16 13:32:16,806 INFO /usr/local/bin/bench new-app buildocustom
2025-06-16 13:32:16,811 LOG creating new app buildocustom
2025-06-16 13:33:05,662 LOG Installing buildocustom
2025-06-16 13:33:05,663 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/buildocustom 
2025-06-16 13:33:07,656 DEBUG bench build --app buildocustom
2025-06-16 13:33:07,814 INFO /usr/local/bin/bench build --app buildocustom
2025-06-16 13:34:22,964 INFO /usr/local/bin/bench --site buildo install-app buildocustom
2025-06-16 13:34:29,995 INFO /usr/local/bin/bench start
2025-06-16 13:34:30,415 INFO /usr/local/bin/bench worker
2025-06-16 13:34:30,432 INFO /usr/local/bin/bench schedule
2025-06-16 13:34:30,458 INFO /usr/local/bin/bench serve --port 8000
2025-06-16 13:34:30,579 INFO /usr/local/bin/bench watch
2025-06-16 14:06:11,869 INFO /usr/local/bin/bench set-config -g developer_mode 1
2025-06-16 14:07:27,514 INFO /usr/local/bin/bench get-app print_designer
2025-06-16 14:07:27,978 LOG Getting print_designer
2025-06-16 14:07:27,979 DEBUG cd ./apps && git clone https://github.com/frappe/print_designer.git  --depth 1 --origin upstream
2025-06-16 14:07:29,362 LOG Installing print_designer
2025-06-16 14:07:29,362 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/print_designer 
2025-06-16 14:07:31,846 DEBUG cd /home/<USER>/frappe-bench/apps/print_designer && yarn install --check-files
2025-06-16 14:07:31,847 WARNING /usr/local/bin/bench get-app print_designer executed with exit code 1
2025-06-16 14:09:07,160 INFO /usr/local/bin/bench start
2025-06-16 14:09:07,608 INFO /usr/local/bin/bench serve --port 8000
2025-06-16 14:09:07,622 INFO /usr/local/bin/bench watch
2025-06-16 14:09:07,623 INFO /usr/local/bin/bench worker
2025-06-16 14:09:07,751 INFO /usr/local/bin/bench schedule
2025-06-16 14:15:37,549 INFO /usr/local/bin/bench start
2025-06-16 14:15:37,945 INFO /usr/local/bin/bench worker
2025-06-16 14:15:37,976 INFO /usr/local/bin/bench schedule
2025-06-16 14:15:37,989 INFO /usr/local/bin/bench serve --port 8000
2025-06-16 14:15:38,141 INFO /usr/local/bin/bench watch
2025-06-16 14:16:20,616 INFO /usr/local/bin/bench get-app print_designer
2025-06-16 14:16:21,069 LOG Getting print_designer
2025-06-16 14:16:21,069 DEBUG cd ./apps && git clone https://github.com/frappe/print_designer.git  --depth 1 --origin upstream
2025-06-16 14:16:22,114 LOG Installing print_designer
2025-06-16 14:16:22,114 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/print_designer 
2025-06-16 14:16:24,164 DEBUG cd /home/<USER>/frappe-bench/apps/print_designer && yarn install --check-files
2025-06-16 14:16:27,509 DEBUG bench build --app print_designer
2025-06-16 14:16:27,662 INFO /usr/local/bin/bench build --app print_designer
2025-06-16 14:17:15,147 INFO /usr/local/bin/bench --site buildo instal-app print_designer
2025-06-16 14:17:30,298 INFO /usr/local/bin/bench --site buildo --instal-app print_designer
2025-06-16 14:19:16,401 INFO /usr/local/bin/bench --site buildo install-app print_designer
2025-06-16 14:19:54,768 INFO /usr/local/bin/bench --site buildo migrate
2025-06-16 14:20:05,625 INFO /usr/local/bin/bench start
2025-06-16 14:20:06,024 INFO /usr/local/bin/bench worker
2025-06-16 14:20:06,067 INFO /usr/local/bin/bench watch
2025-06-16 14:20:06,126 INFO /usr/local/bin/bench serve --port 8000
2025-06-16 14:20:06,277 INFO /usr/local/bin/bench schedule
2025-06-16 14:20:13,309 INFO /usr/local/bin/bench --site buildo migrate
2025-06-16 18:00:01,842 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-18 15:31:16,685 INFO /usr/local/bin/bench start
2025-06-18 15:31:17,100 INFO /usr/local/bin/bench schedule
2025-06-18 15:31:17,118 INFO /usr/local/bin/bench worker
2025-06-18 15:31:17,118 INFO /usr/local/bin/bench watch
2025-06-18 15:31:17,118 INFO /usr/local/bin/bench serve --port 8000
2025-06-18 18:00:01,795 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-19 14:49:25,592 INFO /usr/local/bin/bench start
2025-06-19 14:49:26,009 INFO /usr/local/bin/bench worker
2025-06-19 14:49:26,014 INFO /usr/local/bin/bench schedule
2025-06-19 14:49:26,019 INFO /usr/local/bin/bench watch
2025-06-19 14:49:26,035 INFO /usr/local/bin/bench serve --port 8000
2025-06-19 18:00:01,423 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-20 09:57:15,334 INFO /usr/local/bin/bench start
2025-06-20 09:57:15,741 INFO /usr/local/bin/bench serve --port 8000
2025-06-20 09:57:15,746 INFO /usr/local/bin/bench schedule
2025-06-20 09:57:15,768 INFO /usr/local/bin/bench worker
2025-06-20 09:57:15,794 INFO /usr/local/bin/bench watch
2025-06-20 12:00:02,011 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-20 12:21:01,308 INFO /usr/local/bin/bench clear-cache
2025-06-20 12:21:18,309 INFO /usr/local/bin/bench build
2025-06-20 13:19:58,764 INFO /usr/local/bin/bench build
2025-06-20 13:20:29,400 INFO /usr/local/bin/bench clear-cache
2025-06-20 13:23:05,362 INFO /usr/local/bin/bench build
2025-06-20 13:23:56,981 INFO /usr/local/bin/bench clear-cache
2025-06-20 14:00:29,277 INFO /usr/local/bin/bench migrate
2025-06-20 14:50:26,572 INFO /usr/local/bin/bench migrate
2025-06-20 15:29:15,930 INFO /usr/local/bin/bench clear-cache
2025-06-20 15:42:27,623 INFO /usr/local/bin/bench clear-cache
2025-06-20 15:42:36,721 INFO /usr/local/bin/bench migrate
2025-06-20 15:49:23,928 INFO /usr/local/bin/bench migrate
2025-06-20 16:00:40,368 INFO /usr/local/bin/bench migrate
2025-06-20 16:05:43,706 INFO /usr/local/bin/bench migrate
2025-06-20 16:11:43,863 INFO /usr/local/bin/bench migrate
2025-06-20 16:21:05,559 INFO /usr/local/bin/bench migrate
2025-06-20 16:25:04,648 INFO /usr/local/bin/bench migrate
2025-06-20 16:25:19,187 INFO /usr/local/bin/bench clear-cache
2025-06-20 16:25:29,911 INFO /usr/local/bin/bench build
2025-06-20 16:25:58,508 INFO /usr/local/bin/bench clear-cache
2025-06-20 16:26:09,272 INFO /usr/local/bin/bench clear-cache
2025-06-20 16:41:35,828 INFO /usr/local/bin/bench migrate
2025-06-20 17:29:10,125 INFO /usr/local/bin/bench migrate
2025-06-20 17:42:39,576 INFO /usr/local/bin/bench migrate
2025-06-20 18:00:01,908 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-20 19:18:13,479 INFO /usr/local/bin/bench migrate
2025-06-20 19:19:09,081 INFO /usr/local/bin/bench clear-cache
2025-06-20 19:19:34,336 INFO /usr/local/bin/bench build
2025-06-20 19:21:03,710 INFO /usr/local/bin/bench clear-cache
2025-06-20 19:50:13,031 INFO /usr/local/bin/bench migrate
2025-06-20 19:51:20,089 INFO /usr/local/bin/bench run-tests buildocustom.suppliers.utils.test_supplier_payment_allocation --verbose
2025-06-20 19:51:28,563 INFO /usr/local/bin/bench run-tests buildocustom.suppliers.utils.test_supplier_payment_allocation
2025-06-20 19:51:37,360 INFO /usr/local/bin/bench run-tests --help
2025-06-20 19:51:46,620 INFO /usr/local/bin/bench run-tests --module buildocustom.suppliers.utils.test_supplier_payment_allocation
2025-06-20 19:51:58,483 INFO /usr/local/bin/bench --site buildo set-config allow_tests true
2025-06-21 16:17:35,289 INFO /usr/local/bin/bench start
2025-06-21 16:17:35,716 INFO /usr/local/bin/bench schedule
2025-06-21 16:17:35,729 INFO /usr/local/bin/bench worker
2025-06-21 16:17:35,733 INFO /usr/local/bin/bench watch
2025-06-21 16:17:35,768 INFO /usr/local/bin/bench serve --port 8000
2025-06-21 16:34:27,321 INFO /usr/local/bin/bench clear-cache
2025-06-21 16:43:40,387 INFO /usr/local/bin/bench clear-cache
2025-06-21 16:59:04,146 INFO /usr/local/bin/bench clear-cache
2025-06-21 16:59:05,211 INFO /usr/local/bin/bench build
2025-06-21 17:55:24,668 INFO /usr/local/bin/bench clear-cache
2025-06-21 17:55:25,767 INFO /usr/local/bin/bench build
2025-06-21 18:00:01,645 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-21 18:02:16,241 INFO /usr/local/bin/bench migrate
2025-06-21 18:03:52,577 INFO /usr/local/bin/bench migrate
2025-06-21 18:04:43,615 INFO /usr/local/bin/bench migrate
2025-06-21 18:05:37,357 INFO /usr/local/bin/bench migrate
2025-06-21 18:11:50,100 INFO /usr/local/bin/bench migrate
2025-06-21 18:14:24,753 INFO /usr/local/bin/bench migrate
2025-06-22 10:27:03,611 INFO /usr/local/bin/bench start
2025-06-22 10:27:04,028 INFO /usr/local/bin/bench serve --port 8000
2025-06-22 10:27:04,048 INFO /usr/local/bin/bench schedule
2025-06-22 10:27:04,055 INFO /usr/local/bin/bench worker
2025-06-22 10:27:04,080 INFO /usr/local/bin/bench watch
2025-06-22 11:28:43,822 INFO /usr/local/bin/bench --site all migrate
2025-06-22 11:51:22,321 INFO /usr/local/bin/bench --site all migrate
2025-06-22 12:00:02,127 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-22 12:11:21,550 INFO /usr/local/bin/bench --site all migrate
2025-06-22 12:12:00,019 INFO /usr/local/bin/bench --site buildo console
2025-06-22 12:18:21,209 INFO /usr/local/bin/bench --site all migrate
2025-06-22 12:27:28,496 INFO /usr/local/bin/bench --site buildo console
2025-06-22 12:28:41,109 INFO /usr/local/bin/bench --site all migrate
2025-06-22 12:33:12,116 INFO /usr/local/bin/bench --site all migrate
2025-06-22 12:42:14,861 INFO /usr/local/bin/bench --site all migrate
2025-06-22 12:42:44,317 INFO /usr/local/bin/bench clear-cache
2025-06-22 12:42:48,860 INFO /usr/local/bin/bench --site all migrate
2025-06-22 13:10:50,845 INFO /usr/local/bin/bench --site all migrate
2025-06-22 13:17:24,657 INFO /usr/local/bin/bench --site all migrate
2025-06-22 14:35:09,971 INFO /usr/local/bin/bench --site all migrate
2025-06-22 14:39:39,804 INFO /usr/local/bin/bench --site all migrate
2025-06-22 14:46:06,904 INFO /usr/local/bin/bench --site all migrate
2025-06-22 14:46:41,606 INFO /usr/local/bin/bench --site all migrate
2025-06-22 15:05:21,194 INFO /usr/local/bin/bench --site all migrate
2025-06-22 15:10:33,850 INFO /usr/local/bin/bench --site all migrate
2025-06-22 15:58:14,730 INFO /usr/local/bin/bench --site all migrate
2025-06-22 16:25:33,724 INFO /usr/local/bin/bench --site all migrate
2025-06-22 16:27:32,426 INFO /usr/local/bin/bench clear-cache
2025-06-22 16:30:48,238 INFO /usr/local/bin/bench clear-cache
2025-06-22 16:30:49,914 INFO /usr/local/bin/bench clear-website-cache
2025-06-22 16:38:02,881 INFO /usr/local/bin/bench clear-cache
2025-06-22 18:00:01,207 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-23 10:15:57,433 INFO /usr/local/bin/bench start
2025-06-23 10:15:57,836 INFO /usr/local/bin/bench serve --port 8000
2025-06-23 10:15:57,845 INFO /usr/local/bin/bench schedule
2025-06-23 10:15:57,866 INFO /usr/local/bin/bench worker
2025-06-23 10:15:57,883 INFO /usr/local/bin/bench watch
2025-06-23 11:11:12,317 INFO /usr/local/bin/bench migrate
2025-06-23 11:13:24,167 INFO /usr/local/bin/bench --site buildo console
2025-06-23 11:21:42,624 INFO /usr/local/bin/bench migrate
2025-06-23 11:27:16,129 INFO /usr/local/bin/bench migrate
2025-06-23 11:28:31,921 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.test_allocation.test_payment_allocation
2025-06-23 11:28:50,774 INFO /usr/local/bin/bench --site buildo execute frappe.get_doc('Payment Received', 'TEST-20250623112833').reload(); print('Payment loaded successfully')
2025-06-23 11:29:23,093 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.debug_allocation.debug_payment_allocation
2025-06-23 11:31:00,737 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.test_new_payment.test_new_payment_allocation
2025-06-23 11:31:38,827 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.test_new_payment.test_new_payment_allocation
2025-06-23 11:31:52,709 INFO /usr/local/bin/bench --site buildo console
2025-06-23 11:35:59,746 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.test_new_payment.test_new_payment_allocation
2025-06-23 11:36:53,416 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.test_new_payment.test_new_payment_allocation
2025-06-23 11:37:15,953 INFO /usr/local/bin/bench migrate
2025-06-23 11:38:19,970 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.simple_test.simple_allocation_test
2025-06-23 11:45:49,597 INFO /usr/local/bin/bench migrate
2025-06-23 11:46:40,501 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.test_clean_allocation.test_clean_allocation
2025-06-23 11:46:57,063 INFO /usr/local/bin/bench --site buildo execute print('Checking invoices...'); invoices = frappe.get_all('Client Invoice', filters={'customer': 'Hiren', 'project': 'Project 2'}, fields=['name', 'balance_amount', 'status']); print(f'Found {len(invoices)} invoices:'); [print(f'  {inv.name}: Balance {inv.balance_amount}, Status: {inv.status}') for inv in invoices]
2025-06-23 11:48:27,819 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.check_data.check_existing_data
2025-06-23 11:49:03,782 INFO /usr/local/bin/bench --site buildo execute buildocustom.customers.utils.test_clean_allocation.test_clean_allocation
2025-06-23 11:49:17,469 INFO /usr/local/bin/bench --site buildo console
2025-06-23 11:58:51,837 INFO /usr/local/bin/bench migrate
2025-06-23 11:59:05,464 INFO /usr/local/bin/bench --site buildo console
2025-06-23 12:00:02,040 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-23 12:00:44,894 INFO /usr/local/bin/bench --site buildo console
2025-06-23 12:04:17,825 INFO /usr/local/bin/bench --site buildo console
2025-06-23 12:08:39,947 INFO /usr/local/bin/bench migrate
2025-06-23 12:17:31,281 INFO /usr/local/bin/bench --site all console
2025-06-23 12:23:21,703 INFO /usr/local/bin/bench --site buildo console
2025-06-23 12:35:50,981 INFO /usr/local/bin/bench --site buildo console
2025-06-23 12:38:06,518 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_draft_allocation.test_draft_allocation
2025-06-23 12:39:57,310 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_update_delete.test_payment_update_delete
2025-06-23 12:46:41,001 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_comprehensive_allocation.test_comprehensive_allocation
2025-06-23 12:48:03,539 INFO /usr/local/bin/bench migrate
2025-06-23 12:48:40,354 INFO /usr/local/bin/bench --site buildo console
2025-06-23 12:50:58,641 INFO /usr/local/bin/bench --site buildo logs
2025-06-23 12:51:34,576 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_allocation_deletion.test_allocation_deletion
2025-06-23 12:53:39,820 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_final_verification.test_final_verification
2025-06-23 12:54:22,397 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_final_verification.test_final_verification
2025-06-23 12:57:46,375 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_final_verification.test_final_verification
2025-06-23 12:58:20,958 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_hook_debug.test_hook_debug
2025-06-23 12:59:07,967 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_complete_system.test_complete_system
2025-06-23 12:59:44,632 INFO /usr/local/bin/bench --site buildo execute buildocustom.test_simple_verification.test_simple_verification
2025-06-23 13:02:31,210 INFO /usr/local/bin/bench migrate
2025-06-23 13:09:00,220 INFO /usr/local/bin/bench migrate
2025-06-23 13:22:08,461 INFO /usr/local/bin/bench migrate
2025-06-23 13:23:25,893 INFO /usr/local/bin/bench console
2025-06-23 13:29:48,629 INFO /usr/local/bin/bench migrate
2025-06-23 15:15:19,651 INFO /usr/local/bin/bench --site all migrate
2025-06-23 15:47:27,395 INFO /usr/local/bin/bench --site site1.local execute frappe.desk.query_report.run --args {"report_name": "Contractor Ledger Side by Side", "filters": {}}
2025-06-23 15:47:35,496 INFO /usr/local/bin/bench --site all list-apps
2025-06-23 16:31:28,008 INFO /usr/local/bin/bench --site all migrate
2025-06-23 16:32:40,152 INFO /usr/local/bin/bench --site all migrate
2025-06-23 16:37:33,874 INFO /usr/local/bin/bench --site all console
2025-06-23 16:39:57,044 INFO /usr/local/bin/bench --site all migrate
2025-06-23 16:40:12,919 INFO /usr/local/bin/bench --site all console
2025-06-23 16:57:49,462 INFO /usr/local/bin/bench --site all migrate
2025-06-23 17:19:57,537 INFO /usr/local/bin/bench --site all migrate
2025-06-23 18:00:01,783 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-24 10:48:13,840 INFO /usr/local/bin/bench start
2025-06-24 10:48:14,292 INFO /usr/local/bin/bench schedule
2025-06-24 10:48:14,293 INFO /usr/local/bin/bench watch
2025-06-24 10:48:14,306 INFO /usr/local/bin/bench serve --port 8000
2025-06-24 10:48:14,311 INFO /usr/local/bin/bench worker
2025-06-24 11:13:55,967 INFO /usr/local/bin/bench --site all migrate
2025-06-24 11:29:26,844 INFO /usr/local/bin/bench --site all migrate
2025-06-24 11:33:09,014 INFO /usr/local/bin/bench --site all migrate
2025-06-24 11:49:08,000 INFO /usr/local/bin/bench --site all sync-dashboards
2025-06-24 11:50:41,681 INFO /usr/local/bin/bench --site all migrate
2025-06-24 11:54:34,658 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.install_dashboard.install_dashboard_components
2025-06-24 11:55:12,145 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_cards.create_number_cards
2025-06-24 11:56:02,781 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_contractor_card.create_contractor_balance_card
2025-06-24 12:00:02,022 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-24 12:10:35,031 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_workspace.create_dashboard_workspace
2025-06-24 12:15:08,412 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.fix_workspace_lists.create_quick_lists
2025-06-24 12:16:26,425 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_custom_workspace.create_custom_blocks
2025-06-24 12:17:53,481 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_simple_workspace.create_simple_workspace
2025-06-24 12:18:56,955 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.final_workspace.create_final_workspace
2025-06-24 12:19:18,077 INFO /usr/local/bin/bench --site all migrate
2025-06-24 12:22:53,563 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.final_workspace.create_final_workspace
2025-06-24 12:23:30,756 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_page.create_invoice_dashboard_page
2025-06-24 12:24:16,073 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_clean_workspace.create_clean_workspace
2025-06-24 12:25:00,338 INFO /usr/local/bin/bench --site all migrate
2025-06-24 12:27:56,240 INFO /usr/local/bin/bench --site all execute buildocustom.buildo.create_embedded_lists.create_embedded_lists_workspace
2025-06-24 12:28:21,004 INFO /usr/local/bin/bench --site all migrate
2025-06-24 12:33:17,860 INFO /usr/local/bin/bench --site buildo.localhost execute buildocustom.buildo.install_dashboard.install_dashboard_components
2025-06-24 12:33:25,974 INFO /usr/local/bin/bench --site all list-apps
2025-06-24 12:34:21,784 INFO /usr/local/bin/bench --site all migrate
2025-06-24 12:35:04,243 INFO /usr/local/bin/bench --site buildo execute buildocustom.buildo.install_dashboard.install_dashboard_components
2025-06-24 12:36:52,891 INFO /usr/local/bin/bench --site buildo execute buildocustom.buildo.install_dashboard.install_dashboard_components
2025-06-24 12:39:36,406 INFO /usr/local/bin/bench --site buildo migrate
2025-06-24 12:39:57,105 INFO /usr/local/bin/bench --site buildo clear-cache
2025-06-24 12:43:22,745 INFO /usr/local/bin/bench --site buildo execute buildocustom.buildo.fix_dashboard_lists.fix_dashboard_quick_lists
2025-06-24 12:43:41,241 INFO /usr/local/bin/bench --site buildo console
2025-06-24 12:47:00,698 INFO /usr/local/bin/bench --site buildo clear-cache
2025-06-24 12:47:02,651 INFO /usr/local/bin/bench restart
2025-06-24 12:47:03,275 INFO A newer version of bench is available: 5.25.4 → 5.25.7
2025-06-24 12:47:44,611 INFO /usr/local/bin/bench --site buildo execute buildocustom.buildo.fix_workspace_simple.fix_workspace_simple
2025-06-24 12:48:51,104 INFO /usr/local/bin/bench --site buildo clear-cache
2025-06-24 12:50:18,041 INFO /usr/local/bin/bench --site buildo clear-cache
2025-06-24 12:50:20,209 INFO /usr/local/bin/bench restart
2025-06-24 12:50:20,547 INFO A newer version of bench is available: 5.25.4 → 5.25.7
2025-06-24 12:50:35,155 INFO /usr/local/bin/bench --site buildo console
2025-06-24 12:52:54,103 INFO /usr/local/bin/bench --site buildo execute buildocustom.buildo.test_quick_lists.test_quick_lists
2025-06-24 12:53:12,370 INFO /usr/local/bin/bench --site all migrate
2025-06-24 13:28:27,988 INFO /usr/local/bin/bench --site all migrate
2025-06-24 13:34:49,790 INFO /usr/local/bin/bench --site all migrate
2025-06-24 13:35:09,681 INFO /usr/local/bin/bench --site buildo console
2025-06-24 13:58:19,978 INFO /usr/local/bin/bench --site all migrate
2025-06-24 14:00:59,494 INFO /usr/local/bin/bench --site buildo clear-cache
2025-06-24 14:02:25,520 INFO /usr/local/bin/bench --site all migrate
2025-06-24 14:04:37,334 INFO /usr/local/bin/bench --site buildo clear-cache
2025-06-24 14:04:54,596 INFO /usr/local/bin/bench --site buildo console
2025-06-24 14:08:24,553 INFO /usr/local/bin/bench --site buildo clear-cache
2025-06-24 14:09:46,312 INFO /usr/local/bin/bench --site buildo console
2025-06-26 15:45:23,541 INFO /usr/local/bin/bench start
2025-06-26 15:45:23,986 INFO /usr/local/bin/bench schedule
2025-06-26 15:45:23,993 INFO /usr/local/bin/bench serve --port 8000
2025-06-26 15:45:24,006 INFO /usr/local/bin/bench watch
2025-06-26 15:45:24,007 INFO /usr/local/bin/bench worker
2025-06-26 16:00:32,235 INFO /usr/local/bin/bench --site all migrate
2025-06-26 18:00:01,661 INFO /usr/local/bin/bench --verbose --site all backup
2025-07-28 19:11:07,405 INFO /usr/local/bin/bench start
2025-07-28 19:11:07,828 INFO /usr/local/bin/bench watch
2025-07-28 19:11:07,833 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 19:11:07,841 INFO /usr/local/bin/bench schedule
2025-07-28 19:11:07,852 INFO /usr/local/bin/bench worker
2025-07-28 21:17:31,484 INFO /usr/local/bin/bench start
2025-07-28 21:17:32,045 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 21:17:32,254 INFO /usr/local/bin/bench worker
2025-07-28 21:17:32,266 INFO /usr/local/bin/bench schedule
2025-07-28 21:17:32,449 INFO /usr/local/bin/bench watch
2025-07-28 21:18:29,420 INFO /usr/local/bin/bench start
2025-07-28 21:18:30,154 INFO /usr/local/bin/bench worker
2025-07-28 21:18:30,169 INFO /usr/local/bin/bench watch
2025-07-28 21:18:30,794 INFO /usr/local/bin/bench schedule
2025-07-28 21:18:57,636 INFO /usr/local/bin/bench start
2025-07-28 21:18:58,061 INFO /usr/local/bin/bench watch
2025-07-28 21:18:58,117 INFO /usr/local/bin/bench schedule
2025-07-28 21:18:58,180 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 21:18:58,289 INFO /usr/local/bin/bench worker
2025-07-28 21:25:59,752 INFO /usr/local/bin/bench start
2025-07-28 21:26:00,826 INFO /usr/local/bin/bench schedule
2025-07-28 21:26:00,863 INFO /usr/local/bin/bench watch
2025-07-28 21:26:00,903 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 21:26:00,920 INFO /usr/local/bin/bench worker
2025-07-28 21:26:58,430 INFO /usr/local/bin/bench build
2025-07-28 21:27:12,309 INFO /usr/local/bin/bench start
2025-07-28 21:27:12,723 INFO /usr/local/bin/bench watch
2025-07-28 21:27:12,763 INFO /usr/local/bin/bench schedule
2025-07-28 21:27:12,965 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 21:27:12,985 INFO /usr/local/bin/bench worker
2025-07-28 21:44:36,489 INFO /usr/local/bin/bench clear-cache
2025-07-28 21:45:22,748 INFO /usr/local/bin/bench --site buildo reinstall_app buildocustom
2025-07-28 21:45:38,543 INFO /usr/local/bin/bench --site buildo uninstall-app buildocustom
2025-07-28 21:46:06,237 INFO /usr/local/bin/bench --site buildo install-app buildocustom
2025-07-28 21:46:33,670 INFO /usr/local/bin/bench --site buildo migrate
2025-07-28 21:47:06,404 INFO /usr/local/bin/bench restart
2025-07-28 21:47:06,916 INFO A newer version of bench is available: 5.25.4 → 5.25.9
2025-07-28 21:47:44,787 INFO /usr/local/bin/bench start
2025-07-28 21:47:45,380 INFO /usr/local/bin/bench worker
2025-07-28 21:47:45,464 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 21:47:45,517 INFO /usr/local/bin/bench schedule
2025-07-28 21:47:45,745 INFO /usr/local/bin/bench watch
2025-07-28 21:48:00,739 INFO /usr/local/bin/bench --site buildo migrate
2025-07-28 21:50:13,065 INFO /usr/local/bin/bench build
2025-07-28 21:50:38,142 INFO /usr/local/bin/bench build
2025-07-28 21:51:30,799 INFO /usr/local/bin/bench update
2025-07-28 21:51:32,569 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-07-28 21:51:43,559 WARNING /usr/local/bin/bench update executed with exit code 1
2025-07-28 21:51:43,897 INFO A newer version of bench is available: 5.25.4 → 5.25.9
2025-07-28 21:51:49,063 INFO /usr/local/bin/bench update
2025-07-28 21:51:50,375 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-07-28 21:52:01,811 WARNING /usr/local/bin/bench update executed with exit code 1
2025-07-28 21:52:02,205 INFO A newer version of bench is available: 5.25.4 → 5.25.9
2025-07-28 21:59:44,496 INFO /usr/local/bin/bench clear-cache
2025-07-28 22:01:06,724 INFO /usr/local/bin/bench clear-cache
2025-07-28 22:06:10,507 INFO /usr/local/bin/bench start
2025-07-28 22:06:11,039 INFO /usr/local/bin/bench schedule
2025-07-28 22:06:11,094 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 22:06:11,133 INFO /usr/local/bin/bench worker
2025-07-28 22:06:11,142 INFO /usr/local/bin/bench watch
2025-07-28 22:08:02,450 INFO /usr/local/bin/bench --site buildo install-app buildocustom
2025-07-28 22:12:49,418 INFO /usr/local/bin/bench start
2025-07-28 22:13:44,592 INFO /usr/local/bin/bench start
2025-07-28 22:13:45,444 INFO /usr/local/bin/bench watch
2025-07-28 22:13:45,767 INFO /usr/local/bin/bench worker
2025-07-28 22:13:46,140 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 22:13:46,141 INFO /usr/local/bin/bench schedule
2025-07-28 22:15:45,107 INFO /usr/local/bin/bench --site buildo migrate
2025-07-28 22:15:56,436 INFO /usr/local/bin/bench start
2025-07-28 22:15:57,276 INFO /usr/local/bin/bench worker
2025-07-28 22:15:57,426 INFO /usr/local/bin/bench serve --port 8000
2025-07-28 22:15:57,751 INFO /usr/local/bin/bench watch
2025-07-28 22:15:58,031 INFO /usr/local/bin/bench schedule
2025-07-28 22:16:33,211 INFO /usr/local/bin/bench --site buildo migrate
2025-07-28 22:18:12,231 INFO /usr/local/bin/bench --site buildo migrate
2025-07-28 22:19:12,380 INFO /usr/local/bin/bench set-config maintenance_mode 0
