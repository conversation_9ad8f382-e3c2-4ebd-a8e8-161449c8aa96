2025-07-28 21:27:44,694 ERROR frappe Failed to run after request hook
Site: buildo
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 196, in init_request
    frappe.local.http_request = HTTPRequest()
                                ^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 44, in __init__
    self.set_session()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 78, in set_session
    frappe.local.login_manager = LoginManager()
                                 ^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 117, in __init__
    self.make_session(resume=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 211, in make_session
    frappe.local.session_obj = Session(
                               ^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 230, in __init__
    self.resume()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 308, in resume
    data = self.get_session_record()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 326, in get_session_record
    r = self.get_session_data()
        ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 343, in get_session_data
    data = self.get_session_data_from_db()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 374, in get_session_data_from_db
    .where(sessions.lastupdate > get_expired_threshold())
                                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 455, in get_expired_threshold
    now = frappe.utils.now()
          ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 361, in now
    return now_datetime().strftime(DATETIME_FORMAT)
           ^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 313, in now_datetime
    dt = convert_utc_to_system_timezone(datetime.datetime.now(pytz.UTC))
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 348, in convert_utc_to_system_timezone
    time_zone = get_system_timezone()
                ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 328, in get_system_timezone
    return frappe.get_system_settings("time_zone") or "Asia/Kolkata"  # Default to India ?!
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2337, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 133, in application
    response = handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/permissions.py", line 870, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 325, in handle_exception
    allow_traceback = frappe.get_system_settings("allow_error_traceback") if frappe.db else False
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2337, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'
2025-07-28 21:35:13,868 ERROR frappe Failed to run after request hook
Site: buildo
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 196, in init_request
    frappe.local.http_request = HTTPRequest()
                                ^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 44, in __init__
    self.set_session()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 78, in set_session
    frappe.local.login_manager = LoginManager()
                                 ^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 117, in __init__
    self.make_session(resume=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 211, in make_session
    frappe.local.session_obj = Session(
                               ^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 230, in __init__
    self.resume()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 308, in resume
    data = self.get_session_record()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 326, in get_session_record
    r = self.get_session_data()
        ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 343, in get_session_data
    data = self.get_session_data_from_db()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 374, in get_session_data_from_db
    .where(sessions.lastupdate > get_expired_threshold())
                                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 455, in get_expired_threshold
    now = frappe.utils.now()
          ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 361, in now
    return now_datetime().strftime(DATETIME_FORMAT)
           ^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 313, in now_datetime
    dt = convert_utc_to_system_timezone(datetime.datetime.now(pytz.UTC))
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 348, in convert_utc_to_system_timezone
    time_zone = get_system_timezone()
                ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 328, in get_system_timezone
    return frappe.get_system_settings("time_zone") or "Asia/Kolkata"  # Default to India ?!
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2337, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 133, in application
    response = handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/permissions.py", line 870, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 325, in handle_exception
    allow_traceback = frappe.get_system_settings("allow_error_traceback") if frappe.db else False
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2337, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'
2025-07-28 21:47:56,368 ERROR frappe Failed to run after request hook
Site: buildo
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 196, in init_request
    frappe.local.http_request = HTTPRequest()
                                ^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 44, in __init__
    self.set_session()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 78, in set_session
    frappe.local.login_manager = LoginManager()
                                 ^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 117, in __init__
    self.make_session(resume=True)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/auth.py", line 211, in make_session
    frappe.local.session_obj = Session(
                               ^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 230, in __init__
    self.resume()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 308, in resume
    data = self.get_session_record()
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 326, in get_session_record
    r = self.get_session_data()
        ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 343, in get_session_data
    data = self.get_session_data_from_db()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 374, in get_session_data_from_db
    .where(sessions.lastupdate > get_expired_threshold())
                                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/sessions.py", line 455, in get_expired_threshold
    now = frappe.utils.now()
          ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 361, in now
    return now_datetime().strftime(DATETIME_FORMAT)
           ^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 313, in now_datetime
    dt = convert_utc_to_system_timezone(datetime.datetime.now(pytz.UTC))
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 348, in convert_utc_to_system_timezone
    time_zone = get_system_timezone()
                ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/data.py", line 328, in get_system_timezone
    return frappe.get_system_settings("time_zone") or "Asia/Kolkata"  # Default to India ?!
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2337, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 133, in application
    response = handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/permissions.py", line 870, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 325, in handle_exception
    allow_traceback = frappe.get_system_settings("allow_error_traceback") if frappe.db else False
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2337, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'
2025-07-28 22:06:29,030 ERROR frappe Failed to run after request hook
Site: buildo
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 100, in application
    init_request(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 184, in init_request
    raise frappe.SessionStopped("Session Stopped")
frappe.exceptions.SessionStopped: Session Stopped

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 133, in application
    response = handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/permissions.py", line 870, in wrapper
    return fn(e, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 325, in handle_exception
    allow_traceback = frappe.get_system_settings("allow_error_traceback") if frappe.db else False
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 2337, in get_system_settings
    local.system_settings = system_settings = get_cached_doc("System Settings")
                                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1179, in get_cached_doc
    doc = get_doc(*args, **kwargs)
          ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1305, in get_doc
    return frappe.model.document.get_doc(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/document.py", line 83, in get_doc
    controller = get_controller(doctype)
                 ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 67, in get_controller
    return import_controller(doctype)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/model/base_document.py", line 89, in import_controller
    class_overrides = frappe.get_hooks("override_doctype_class")
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 57, in wrapper
    return frappe.local.request_cache[func][args_key]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
KeyError: 5740354900026072187

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 147, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    for after_request_task in frappe.get_hooks("after_request"):
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1616, in get_hooks
    hooks = _dict(_load_app_hooks())
                  ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/caching.py", line 59, in wrapper
    return_val = func(*args, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1585, in _load_app_hooks
    app_hooks = get_module(f"{app}.hooks")
                ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1451, in get_module
    return importlib.import_module(modulename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1324, in _find_and_load_unlocked
ModuleNotFoundError: No module named 'buildocustom.hooks'
