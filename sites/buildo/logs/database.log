2025-06-23 16:32:41,256 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 16:32:43,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-23 16:32:43,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-23 16:32:43,714 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-23 16:32:43,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 16:32:44,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0
2025-06-23 16:32:44,918 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0
2025-06-23 16:32:45,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0
2025-06-23 16:32:45,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-23 16:32:45,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-23 16:32:45,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0
2025-06-23 16:39:58,139 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 16:39:59,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 16:40:00,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-23 16:40:00,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0
2025-06-23 16:40:00,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 16:40:00,660 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0
2025-06-23 16:40:01,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-23 16:40:01,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-23 16:40:01,589 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0
2025-06-23 16:40:01,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 16:40:01,886 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0
2025-06-23 16:57:50,613 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 16:57:52,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-23 16:57:53,319 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-23 16:57:53,455 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-23 16:57:53,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 16:57:53,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0
2025-06-23 16:57:54,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `area` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0
2025-06-23 16:57:54,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-23 16:57:55,169 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-23 16:57:55,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-23 16:57:55,558 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0
2025-06-23 17:19:58,300 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-23 17:19:59,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0
2025-06-23 17:20:00,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-23 17:20:00,308 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0
2025-06-23 17:20:00,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 17:20:00,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0
2025-06-23 17:20:01,269 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `area` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-23 17:20:01,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-23 17:20:01,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0
2025-06-23 17:20:01,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-23 17:20:01,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0
2025-06-24 10:50:10,225 WARNING database DDL Query made to DB:
create table `tabPetty Cash` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-24 10:53:54,453 WARNING database DDL Query made to DB:
create table `tabPetty Cash Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-24 10:56:00,474 WARNING database DDL Query made to DB:
alter table `tabPetty Cash Table` add column if not exists parent varchar(140)
2025-06-24 10:56:00,475 WARNING database DDL Query made to DB:
alter table `tabPetty Cash Table` add column if not exists parenttype varchar(140)
2025-06-24 10:56:00,476 WARNING database DDL Query made to DB:
alter table `tabPetty Cash Table` add column if not exists parentfield varchar(140)
2025-06-24 10:56:00,585 WARNING database DDL Query made to DB:
ALTER TABLE `tabPetty Cash Table` ADD COLUMN `date` date, ADD COLUMN `type` varchar(140) default 'Expense', ADD COLUMN `receive_amt` decimal(21,9) not null default 0, ADD COLUMN `exp_amt` decimal(21,9) not null default 0, ADD COLUMN `remark` varchar(140)
2025-06-24 10:56:36,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabPetty Cash` ADD COLUMN `start_date` date, ADD COLUMN `project` varchar(140), ADD COLUMN `staff` varchar(140), ADD COLUMN `total_petty_cash_received` decimal(21,9) not null default 0, ADD COLUMN `total_expense` decimal(21,9) not null default 0, ADD COLUMN `balance_in_hand` decimal(21,9) not null default 0, ADD COLUMN `notes` text
2025-06-24 10:57:17,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabPetty Cash` MODIFY `total_petty_cash_received` decimal(21,9) not null default 0, MODIFY `balance_in_hand` decimal(21,9) not null default 0, MODIFY `total_expense` decimal(21,9) not null default 0
2025-06-24 10:58:39,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabPetty Cash` MODIFY `balance_in_hand` decimal(21,9) not null default 0, MODIFY `total_petty_cash_received` decimal(21,9) not null default 0, MODIFY `total_expense` decimal(21,9) not null default 0
2025-06-24 11:13:57,395 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 11:13:59,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0
2025-06-24 11:14:00,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 11:14:00,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 11:14:00,358 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:14:00,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0
2025-06-24 11:14:01,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-06-24 11:14:01,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0
2025-06-24 11:14:01,679 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 11:14:01,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:14:02,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0
2025-06-24 11:14:02,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabPetty Cash` ADD COLUMN `opening_balance` decimal(21,9) not null default 0
2025-06-24 11:14:02,240 WARNING database DDL Query made to DB:
ALTER TABLE `tabPetty Cash` MODIFY `balance_in_hand` decimal(21,9) not null default 0, MODIFY `total_petty_cash_received` decimal(21,9) not null default 0, MODIFY `total_expense` decimal(21,9) not null default 0
2025-06-24 11:14:02,410 WARNING database DDL Query made to DB:
ALTER TABLE `tabPetty Cash Table` MODIFY `receive_amt` decimal(21,9) not null default 0, MODIFY `exp_amt` decimal(21,9) not null default 0
2025-06-24 11:29:27,642 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 11:29:29,299 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-24 11:29:29,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 11:29:29,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0
2025-06-24 11:29:29,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:29:30,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0
2025-06-24 11:29:30,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-24 11:29:30,947 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-24 11:29:31,162 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 11:29:31,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:29:31,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0
2025-06-24 11:33:10,179 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 11:33:12,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-24 11:33:12,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 11:33:13,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0
2025-06-24 11:33:13,307 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:33:13,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0
2025-06-24 11:33:14,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0
2025-06-24 11:33:14,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:33:15,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 11:33:15,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-24 11:33:15,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0
2025-06-24 11:50:42,558 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 11:50:44,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 11:50:44,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 11:50:44,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 11:50:45,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:50:45,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0
2025-06-24 11:50:46,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-06-24 11:50:46,172 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-24 11:50:46,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 11:50:46,499 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 11:50:46,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0
2025-06-24 12:19:18,861 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 12:19:21,678 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0
2025-06-24 12:19:22,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 12:19:22,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:19:22,799 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:19:23,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-24 12:19:23,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-24 12:19:23,915 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:19:24,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0
2025-06-24 12:19:24,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:19:24,448 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0
2025-06-24 12:25:01,173 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 12:25:02,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:25:03,199 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 12:25:03,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:25:03,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:25:03,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0
2025-06-24 12:25:04,327 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-06-24 12:25:04,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:25:04,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0
2025-06-24 12:25:04,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:25:05,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0
2025-06-24 12:28:22,069 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 12:28:23,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:28:24,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 12:28:24,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 12:28:24,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:28:24,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0
2025-06-24 12:28:25,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `area` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-06-24 12:28:25,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:28:26,026 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0
2025-06-24 12:28:26,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:28:26,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0
2025-06-24 12:34:22,943 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 12:39:37,761 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 12:39:39,705 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-24 12:39:40,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 12:39:40,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 12:39:40,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:39:40,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0
2025-06-24 12:39:41,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-06-24 12:39:41,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:39:42,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 12:39:42,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-24 12:39:42,727 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0
2025-06-24 12:53:13,337 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 12:53:15,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-24 12:53:15,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 12:53:15,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 12:53:16,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:53:16,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0
2025-06-24 12:53:18,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `area` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 12:53:19,103 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:53:19,378 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 12:53:19,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 12:53:19,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0
2025-06-24 13:28:28,743 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 13:28:30,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-24 13:28:30,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 13:28:30,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 13:28:30,856 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 13:28:31,152 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0
2025-06-24 13:28:31,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0
2025-06-24 13:28:31,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 13:28:32,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 13:28:32,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-24 13:28:32,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0
2025-06-24 13:34:50,939 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 13:34:52,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 13:34:53,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 13:34:53,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 13:34:53,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 13:34:53,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0
2025-06-24 13:34:54,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `area` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0
2025-06-24 13:34:54,847 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0
2025-06-24 13:34:55,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 13:34:55,262 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-24 13:34:55,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0
2025-06-24 13:58:20,770 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 13:58:22,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 13:58:23,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 13:58:23,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 13:58:23,493 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 13:58:23,795 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0
2025-06-24 13:58:24,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-24 13:58:24,894 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 13:58:25,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 13:58:25,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 13:58:25,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0
2025-06-24 14:02:26,698 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-24 14:02:28,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0
2025-06-24 14:02:29,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 14:02:29,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0
2025-06-24 14:02:29,539 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 14:02:29,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `total` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0
2025-06-24 14:02:30,843 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `area` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0
2025-06-24 14:02:30,999 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-24 14:02:31,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0
2025-06-24 14:02:31,439 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-24 14:02:31,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0
2025-06-26 16:00:33,073 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-26 16:00:34,731 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0
2025-06-26 16:00:35,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `progress_` decimal(21,9) not null default 0
2025-06-26 16:00:35,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-26 16:00:35,487 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0
2025-06-26 16:00:35,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0
2025-06-26 16:00:36,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabProjects` MODIFY `total_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `progress_` decimal(21,9) not null default 0, MODIFY `area` decimal(21,9) not null default 0
2025-06-26 16:00:36,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Rate Invoice Items` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `curr_bill_qty` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `pre_bill_qty` decimal(21,9) not null default 0
2025-06-26 16:00:37,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabBuiltup Invoice Items` MODIFY `prev_perc` decimal(21,9) not null default 0, MODIFY `total_perc` decimal(21,9) not null default 0, MODIFY `curr_perc` decimal(21,9) not null default 0, MODIFY `pre_bill_amt` decimal(21,9) not null default 0, MODIFY `curr_bill_amt` decimal(21,9) not null default 0
2025-06-26 16:00:37,160 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Received` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-26 16:00:37,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-07-28 19:17:21,521 WARNING database DDL Query made to DB:
ALTER TABLE `tabClient Invoice` MODIFY `tds_amount` decimal(21,9) not null default 0, MODIFY `cgst_` decimal(21,9) not null default 0, MODIFY `cgst_amount` decimal(21,9) not null default 0, MODIFY `tds_` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `sgst_` decimal(21,9) not null default 0, MODIFY `retention_amount` decimal(21,9) not null default 0, MODIFY `retention_` decimal(21,9) not null default 0, MODIFY `sgst_amount` decimal(21,9) not null default 0, MODIFY `igst_amount` decimal(21,9) not null default 0, MODIFY `igst_` decimal(21,9) not null default 0, MODIFY `sub_total` decimal(21,9) not null default 0, MODIFY `balance_amount` decimal(21,9) not null default 0, MODIFY `net_payable` decimal(21,9) not null default 0
2025-07-28 22:16:35,827 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-28 22:18:13,874 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
